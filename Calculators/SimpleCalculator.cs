// using Calculators.ContentSafety;

namespace Calculators
{
    public class SimpleCalculator
    {
        private decimal LastResult { get; set; }

        // private readonly IContentSafetyService _safetyService;
        // private readonly List<SafetyClassification> _safetyLog;

        public SimpleCalculator()
        {
            Reset();
        }

        public SimpleCalculator(IContentSafetyService safetyService)
        {
            _safetyService = safetyService;
            _safetyLog = new List<SafetyClassification>();
        }


        public SimpleCalculator Reset()
        {
            LastResult = 0.0M;
            return this;
        }

        public SimpleCalculator Enter(decimal number)
        {
            LastResult = number;
            return this;
        }

        public SimpleCalculator Plus(decimal number)
        {
            LastResult += number;
            return this;
        }

        public SimpleCalculator Minus(decimal number)
        {
            LastResult -= number;
            return this;
        }

        public SimpleCalculator MultiplyBy(decimal number)
        {
            LastResult *= number;
            return this;
        }

        public SimpleCalculator DivideBy(decimal number)
        {
            LastResult /= number;
            return this;
        }

        public decimal Equals()
        {
            return LastResult;
        }

        public async Task<decimal> EqualsAsync()
        {
            var result = Equals();
            var resultText = $"The calculation result is {result}";
        
            var safetyResult = await _safetyService.ValidateContentAsync(resultText);
            _safetyLog.Add(safetyResult);
        
            if (!safetyResult.IsSafe)
            {
                throw new UnsafeContentException($"Content validation failed: {safetyResult.Category}", safetyResult);
            }
        
            return result;
        }

        public IReadOnlyList<SafetyClassification> GetSafetyLog()
        {
            return _safetyLog.AsReadOnly();
        }

        public async Task<SafetyClassification> ValidateMessageAsync(string message)
        {
            var result = await _safetyService.ValidateContentAsync(message);
            _safetyLog.Add(result);
            return result;
        }
    }
}
